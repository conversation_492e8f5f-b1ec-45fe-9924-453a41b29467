'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useUpdateProfile } from '@/hooks/use-profile';
import type { UserWithProfile } from '@/types/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save } from 'lucide-react';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

interface ProfileSettingsProps {
  user: UserWithProfile;
}

// Profile update schema
const profileUpdateSchema = z.object({
  name: z
    .string()
    .min(1, 'Full name is required')
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must not exceed 100 characters'),
  phone_number: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val || val.trim() === '') return true;
        return /^(\+?6?01[0-46-9]-*[0-9]{7,8})$/.test(val);
      },
      {
        message: 'Please enter a valid Malaysian phone number',
      },
    ),
});

type ProfileUpdateFormValues = z.infer<typeof profileUpdateSchema>;

export function ProfileSettings({ user }: ProfileSettingsProps) {
  const t = useTranslations('settingsPage.profile');
  const updateProfileMutation = useUpdateProfile();

  const form = useForm<ProfileUpdateFormValues>({
    resolver: zodResolver(profileUpdateSchema),
    defaultValues: {
      name: user.profile?.name || '',
      phone_number: user.profile?.phone_number || '',
    },
  });

  const onSubmit = async (values: ProfileUpdateFormValues) => {
    try {
      await updateProfileMutation.mutateAsync({
        userId: user.id,
        updates: {
          name: values.name,
          phone_number: values.phone_number || null,
        },
      });

      toast.success(t('updateSuccess'));
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(t('updateError'));
    }
  };

  const isLoading = updateProfileMutation.isPending;
  const isDirty = form.formState.isDirty;

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t('personalInfo')}</h3>
        <p className="text-sm text-muted-foreground">
          Update your personal information and contact details.
        </p>
      </div>

      <Separator />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Full Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('fullName')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('fullNamePlaceholder')}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone Number */}
            <FormField
              control={form.control}
              name="phone_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('phoneNumber')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('phoneNumberPlaceholder')}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Email (Read-only) */}
          <div className="space-y-2">
            <FormLabel>{t('email')}</FormLabel>
            <Input
              value={user.profile?.email || user.email || ''}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground">{t('emailNote')}</p>
          </div>

          {/* IC Number (Read-only for contractors) */}
          {user.profile?.ic_number && (
            <div className="space-y-2">
              <FormLabel>{t('icNumber')}</FormLabel>
              <Input
                value={user.profile.ic_number}
                disabled
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">
                IC number cannot be changed after registration
              </p>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isLoading || !isDirty}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('updating')}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {t('updateProfile')}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
