# Account Settings Page

This directory contains the comprehensive account settings page for the contractor portal.

## Overview

The account settings page provides contractors with a centralized location to manage their account preferences, profile information, security settings, and notification preferences.

## Features

### 📋 **Profile Settings**
- Update personal information (name, phone number)
- View read-only fields (email, IC number)
- Real-time form validation
- Success/error feedback

### 🔒 **Security Settings**
- Change password with current password verification
- Password strength requirements
- Show/hide password toggles
- Two-factor authentication placeholder (future feature)

### 🔔 **Notification Settings**
- Email notification preferences
  - Project updates
  - Maintenance reminders
  - System alerts
  - Weekly reports
- Browser notification settings
- Persistent settings storage

### ⚙️ **Preferences**
- Language selection (English/Malay)
- Timezone configuration
- Date format preferences
- Theme selection (placeholder for future implementation)

## File Structure

```
src/app/[locale]/(app)/settings/
├── page.tsx                           # Main settings page with tabs
├── components/
│   ├── profile-settings.tsx          # Profile information form
│   ├── security-settings.tsx         # Password and security settings
│   ├── notification-settings.tsx     # Notification preferences
│   └── preferences-settings.tsx      # User preferences
└── README.md                         # This file
```

## Components

### `page.tsx`
- Main settings page with tabbed interface
- Loading and error states
- Navigation back to profile
- Role-based access control

### `profile-settings.tsx`
- Form for updating personal information
- Uses `useUpdateProfile` hook for API calls
- Validation with Zod schema
- Read-only fields for sensitive data

### `security-settings.tsx`
- Password change functionality
- Current password verification
- Password strength indicators
- Future 2FA integration placeholder

### `notification-settings.tsx`
- Toggle switches for various notification types
- Local storage for preference persistence
- Email and push notification categories

### `preferences-settings.tsx`
- Language, timezone, and theme settings
- Date format configuration
- Local storage for user preferences

## Permissions

- **Contractors**: Full access to all settings sections
- **Admins**: Full access to all settings sections
- **Viewers**: No access (redirected)

## Translations

Full bilingual support with translations in:
- `messages/en.json` - English translations
- `messages/ms.json` - Malay translations

## Navigation

Accessible via:
- Profile page "Account Settings" button
- Direct URL: `/settings`
- Navigation menu (for admin users)

## Technical Details

### Dependencies
- React Hook Form with Zod validation
- Radix UI components (Tabs, Switch, Select)
- Lucide React icons
- Next.js internationalization
- Supabase for authentication

### State Management
- React Query for server state
- Local storage for user preferences
- Form state with React Hook Form

### Styling
- Tailwind CSS for styling
- Consistent with application design system
- Responsive design for mobile and desktop

## Future Enhancements

1. **Two-Factor Authentication**
   - SMS/Email verification
   - Authenticator app integration

2. **Theme System**
   - Light/Dark mode implementation
   - Custom theme preferences

3. **Advanced Notifications**
   - Push notification integration
   - Notification scheduling

4. **Account Management**
   - Account deletion functionality
   - Data export features

5. **Session Management**
   - Active session monitoring
   - Remote session termination

## Usage

1. Navigate to `/settings` or click "Account Settings" from profile page
2. Use tabs to switch between different settings categories
3. Make changes and save using the respective save buttons
4. Changes are persisted and take effect immediately

## Testing

To test the settings page:
1. Log in as a contractor user
2. Navigate to Profile page
3. Click "Account Settings" button
4. Test each tab's functionality
5. Verify form validation and save operations
