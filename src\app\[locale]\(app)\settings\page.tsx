'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useUserWithProfile } from '@/hooks/use-auth';
import { usePermissions } from '@/hooks/use-permissions';
import {
  ArrowLeft,
  Bell,
  Lock,
  Settings as SettingsIcon,
  User,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { NotificationSettings } from './components/notification-settings';
import { PreferencesSettings } from './components/preferences-settings';
import { ProfileSettings } from './components/profile-settings';
import { SecuritySettings } from './components/security-settings';

export default function SettingsPage() {
  const {
    data: user,
    isLoading: userLoading,
    refetch: _refetchUser,
  } = useUserWithProfile();
  const {
    userRole,
    isContractor,
    isJKR,
    isClient,
    isLoading: permissionsLoading,
  } = usePermissions();
  const router = useRouter();
  const t = useTranslations('settingsPage');
  const common = useTranslations('common');

  const isLoading = userLoading || permissionsLoading;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-4xl mx-auto">
            <Card>
              <CardContent className="flex items-center justify-center py-16">
                <div className="flex flex-col items-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                  <div className="text-center space-y-2">
                    <p className="text-lg font-medium text-foreground">
                      {t('loading.title')}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t('loading.description')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Error state - no user data
  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-4xl mx-auto">
            <Card className="border-destructive/20">
              <CardHeader>
                <CardTitle className="text-destructive">
                  Unable to Load Settings
                </CardTitle>
                <CardDescription>
                  We couldn't load your settings. Please try refreshing the page.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        window.location.reload();
                      }
                    }}
                  >
                    Refresh Page
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/profile')}
                  >
                    Back to Profile
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        <div className="w-full max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/profile')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>{t('common.back')}</span>
            </Button>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <SettingsIcon className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">
                  {t('title')}
                </h1>
                <p className="text-lg text-muted-foreground">
                  {t('description')}
                </p>
              </div>
            </div>
          </div>

          {/* Settings Content */}
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="profile" className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {t('sections.profile.title')}
                </span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center space-x-2">
                <Lock className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {t('sections.security.title')}
                </span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center space-x-2">
                <Bell className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {t('sections.notifications.title')}
                </span>
              </TabsTrigger>
              <TabsTrigger value="preferences" className="flex items-center space-x-2">
                <SettingsIcon className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {t('sections.preferences.title')}
                </span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('sections.profile.title')}</CardTitle>
                  <CardDescription>
                    {t('sections.profile.description')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ProfileSettings user={user} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('sections.security.title')}</CardTitle>
                  <CardDescription>
                    {t('sections.security.description')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <SecuritySettings user={user} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('sections.notifications.title')}</CardTitle>
                  <CardDescription>
                    {t('sections.notifications.description')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NotificationSettings user={user} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preferences" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('sections.preferences.title')}</CardTitle>
                  <CardDescription>
                    {t('sections.preferences.description')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PreferencesSettings user={user} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
