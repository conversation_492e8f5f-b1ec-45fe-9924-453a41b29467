'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import type { UserWithProfile } from '@/types/auth';
import { Globe, Loader2, Monitor, Palette } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { toast } from 'sonner';

interface PreferencesSettingsProps {
  user: UserWithProfile;
}

interface UserPreferences {
  language: string;
  timezone: string;
  dateFormat: string;
  theme: string;
}

export function PreferencesSettings({ user }: PreferencesSettingsProps) {
  const t = useTranslations('settingsPage.preferences');
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(false);
  const [preferences, setPreferences] = React.useState<UserPreferences>({
    language: 'en',
    timezone: 'Asia/Kuala_Lumpur',
    dateFormat: 'DD/MM/YYYY',
    theme: 'system',
  });

  // Load preferences from localStorage on mount
  React.useEffect(() => {
    const savedPreferences = localStorage.getItem(`user-preferences-${user.id}`);
    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences));
      } catch (error) {
        console.error('Error loading user preferences:', error);
      }
    }
  }, [user.id]);

  const updatePreference = (key: keyof UserPreferences, value: string) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Save to localStorage (in a real app, this would be saved to the backend)
      localStorage.setItem(`user-preferences-${user.id}`, JSON.stringify(preferences));
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Handle language change
      if (preferences.language !== 'en') {
        // In a real app, you would redirect to the new locale
        // For now, we'll just show a message
        toast.success(t('preferencesSaved'));
        toast.info('Language change will take effect on next page reload.');
      } else {
        toast.success(t('preferencesSaved'));
      }
    } catch (error) {
      console.error('Error saving user preferences:', error);
      toast.error(t('preferencesError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Language Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>{t('language')}</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            {t('languageDesc')}
          </p>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>{t('language')}</Label>
            <Select
              value={preferences.language}
              onValueChange={(value) => updatePreference('language', value)}
            >
              <SelectTrigger className="w-full max-w-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">{t('english')}</SelectItem>
                <SelectItem value="ms">{t('malay')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Regional Settings */}
      <div className="space-y-6">
        <Separator />
        
        <div>
          <h3 className="text-lg font-medium">Regional Settings</h3>
          <p className="text-sm text-muted-foreground">
            Configure timezone and date format preferences.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Timezone */}
          <div className="space-y-2">
            <Label>{t('timezone')}</Label>
            <Select
              value={preferences.timezone}
              onValueChange={(value) => updatePreference('timezone', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Asia/Kuala_Lumpur">Malaysia (GMT+8)</SelectItem>
                <SelectItem value="Asia/Singapore">Singapore (GMT+8)</SelectItem>
                <SelectItem value="Asia/Bangkok">Thailand (GMT+7)</SelectItem>
                <SelectItem value="Asia/Jakarta">Indonesia (GMT+7)</SelectItem>
                <SelectItem value="UTC">UTC (GMT+0)</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">{t('timezoneDesc')}</p>
          </div>

          {/* Date Format */}
          <div className="space-y-2">
            <Label>{t('dateFormat')}</Label>
            <Select
              value={preferences.dateFormat}
              onValueChange={(value) => updatePreference('dateFormat', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="DD/MM/YYYY">DD/MM/YYYY (31/12/2024)</SelectItem>
                <SelectItem value="MM/DD/YYYY">MM/DD/YYYY (12/31/2024)</SelectItem>
                <SelectItem value="YYYY-MM-DD">YYYY-MM-DD (2024-12-31)</SelectItem>
                <SelectItem value="DD-MM-YYYY">DD-MM-YYYY (31-12-2024)</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">{t('dateFormatDesc')}</p>
          </div>
        </div>
      </div>

      {/* Theme Settings */}
      <div className="space-y-6">
        <Separator />
        
        <div>
          <h3 className="text-lg font-medium flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span>{t('theme')}</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            {t('themeDesc')}
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>{t('theme')}</Label>
            <Select
              value={preferences.theme}
              onValueChange={(value) => updatePreference('theme', value)}
            >
              <SelectTrigger className="w-full max-w-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">{t('light')}</SelectItem>
                <SelectItem value="dark">{t('dark')}</SelectItem>
                <SelectItem value="system">{t('system')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="bg-muted/50 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground">
              <Monitor className="h-4 w-4 inline mr-2" />
              Theme changes are currently not implemented but will be available in a future update.
            </p>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6">
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="min-w-[140px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('savingPreferences')}
            </>
          ) : (
            <>
              <Palette className="mr-2 h-4 w-4" />
              {t('savePreferences')}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
