'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { supabase } from '@/lib/supabase';
import type { UserWithProfile } from '@/types/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Loader2, Lock, Shield } from 'lucide-react';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

interface SecuritySettingsProps {
  user: UserWithProfile;
}

// Password update schema
const passwordUpdateSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
      .regex(/^(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
      .regex(/^(?=.*\d)/, 'Password must contain at least one number')
      .regex(/^(?=.*[@$!%*?&])/, 'Password must contain at least one special character'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type PasswordUpdateFormValues = z.infer<typeof passwordUpdateSchema>;

export function SecuritySettings({ user }: SecuritySettingsProps) {
  const t = useTranslations('settingsPage.security');
  const [showCurrentPassword, setShowCurrentPassword] = React.useState(false);
  const [showNewPassword, setShowNewPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = React.useState(false);

  const form = useForm<PasswordUpdateFormValues>({
    resolver: zodResolver(passwordUpdateSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const onSubmitPassword = async (values: PasswordUpdateFormValues) => {
    setIsUpdatingPassword(true);
    try {
      // First verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email!,
        password: values.currentPassword,
      });

      if (signInError) {
        toast.error('Current password is incorrect');
        setIsUpdatingPassword(false);
        return;
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: values.newPassword,
      });

      if (updateError) {
        console.error('Password update error:', updateError);
        toast.error(t('passwordUpdateError'));
        setIsUpdatingPassword(false);
        return;
      }

      toast.success(t('passwordUpdateSuccess'));
      form.reset();
    } catch (error) {
      console.error('Password update error:', error);
      toast.error(t('passwordUpdateError'));
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  const isDirty = form.formState.isDirty;

  return (
    <div className="space-y-8">
      {/* Change Password Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium flex items-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>{t('changePassword')}</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            Update your password to keep your account secure.
          </p>
        </div>

        <Separator />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmitPassword)} className="space-y-4">
            {/* Current Password */}
            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('currentPassword')}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showCurrentPassword ? 'text' : 'password'}
                        placeholder={t('currentPasswordPlaceholder')}
                        {...field}
                        disabled={isUpdatingPassword}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        disabled={isUpdatingPassword}
                      >
                        {showCurrentPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* New Password */}
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('newPassword')}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showNewPassword ? 'text' : 'password'}
                        placeholder={t('newPasswordPlaceholder')}
                        {...field}
                        disabled={isUpdatingPassword}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        disabled={isUpdatingPassword}
                      >
                        {showNewPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Confirm Password */}
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('confirmPassword')}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder={t('confirmPasswordPlaceholder')}
                        {...field}
                        disabled={isUpdatingPassword}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={isUpdatingPassword}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Password Requirements */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm font-medium mb-2">{t('passwordRequirements')}</p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• {t('passwordReq1')}</li>
                <li>• {t('passwordReq2')}</li>
                <li>• {t('passwordReq3')}</li>
                <li>• {t('passwordReq4')}</li>
                <li>• {t('passwordReq5')}</li>
              </ul>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isUpdatingPassword || !isDirty}
                className="min-w-[140px]"
              >
                {isUpdatingPassword ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('updatingPassword')}
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-4 w-4" />
                    {t('updatePassword')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>

      {/* Two-Factor Authentication Section */}
      <div className="space-y-4">
        <Separator />
        <div>
          <h3 className="text-lg font-medium flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>{t('twoFactor')}</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            {t('twoFactorDescription')}
          </p>
        </div>
        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="text-sm text-muted-foreground">
            Two-factor authentication is not yet available. This feature will be added in a future update.
          </p>
        </div>
      </div>
    </div>
  );
}
