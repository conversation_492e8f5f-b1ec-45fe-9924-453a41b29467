'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import type { UserWithProfile } from '@/types/auth';
import { Bell, Loader2, Mail, Monitor } from 'lucide-react';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { toast } from 'sonner';

interface NotificationSettingsProps {
  user: UserWithProfile;
}

interface NotificationPreferences {
  emailNotifications: {
    projectUpdates: boolean;
    maintenanceReminders: boolean;
    systemAlerts: boolean;
    weeklyReports: boolean;
  };
  pushNotifications: {
    browserNotifications: boolean;
  };
}

export function NotificationSettings({ user }: NotificationSettingsProps) {
  const t = useTranslations('settingsPage.notifications');
  const [isLoading, setIsLoading] = React.useState(false);
  const [preferences, setPreferences] = React.useState<NotificationPreferences>({
    emailNotifications: {
      projectUpdates: true,
      maintenanceReminders: true,
      systemAlerts: true,
      weeklyReports: false,
    },
    pushNotifications: {
      browserNotifications: false,
    },
  });

  // Load preferences from localStorage on mount
  React.useEffect(() => {
    const savedPreferences = localStorage.getItem(`notification-preferences-${user.id}`);
    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences));
      } catch (error) {
        console.error('Error loading notification preferences:', error);
      }
    }
  }, [user.id]);

  const updateEmailNotification = (key: keyof NotificationPreferences['emailNotifications'], value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      emailNotifications: {
        ...prev.emailNotifications,
        [key]: value,
      },
    }));
  };

  const updatePushNotification = (key: keyof NotificationPreferences['pushNotifications'], value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      pushNotifications: {
        ...prev.pushNotifications,
        [key]: value,
      },
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Save to localStorage (in a real app, this would be saved to the backend)
      localStorage.setItem(`notification-preferences-${user.id}`, JSON.stringify(preferences));
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(t('notificationsSaved'));
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error(t('notificationsError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Email Notifications Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>{t('emailNotifications')}</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            Configure which email notifications you want to receive.
          </p>
        </div>

        <Separator />

        <div className="space-y-6">
          {/* Project Updates */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">{t('projectUpdates')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('projectUpdatesDesc')}
              </p>
            </div>
            <Switch
              checked={preferences.emailNotifications.projectUpdates}
              onCheckedChange={(checked) => updateEmailNotification('projectUpdates', checked)}
            />
          </div>

          {/* Maintenance Reminders */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">{t('maintenanceReminders')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('maintenanceRemindersDesc')}
              </p>
            </div>
            <Switch
              checked={preferences.emailNotifications.maintenanceReminders}
              onCheckedChange={(checked) => updateEmailNotification('maintenanceReminders', checked)}
            />
          </div>

          {/* System Alerts */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">{t('systemAlerts')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('systemAlertsDesc')}
              </p>
            </div>
            <Switch
              checked={preferences.emailNotifications.systemAlerts}
              onCheckedChange={(checked) => updateEmailNotification('systemAlerts', checked)}
            />
          </div>

          {/* Weekly Reports */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">{t('weeklyReports')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('weeklyReportsDesc')}
              </p>
            </div>
            <Switch
              checked={preferences.emailNotifications.weeklyReports}
              onCheckedChange={(checked) => updateEmailNotification('weeklyReports', checked)}
            />
          </div>
        </div>
      </div>

      {/* Push Notifications Section */}
      <div className="space-y-6">
        <Separator />
        
        <div>
          <h3 className="text-lg font-medium flex items-center space-x-2">
            <Monitor className="h-5 w-5" />
            <span>{t('pushNotifications')}</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            Configure browser and device notifications.
          </p>
        </div>

        <div className="space-y-6">
          {/* Browser Notifications */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">{t('browserNotifications')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('browserNotificationsDesc')}
              </p>
            </div>
            <Switch
              checked={preferences.pushNotifications.browserNotifications}
              onCheckedChange={(checked) => updatePushNotification('browserNotifications', checked)}
            />
          </div>
        </div>

        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="text-sm text-muted-foreground">
            <Bell className="h-4 w-4 inline mr-2" />
            Push notifications require browser permission. You may be prompted to allow notifications when enabling this feature.
          </p>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6">
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="min-w-[140px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('savingNotifications')}
            </>
          ) : (
            <>
              <Bell className="mr-2 h-4 w-4" />
              {t('saveNotifications')}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
